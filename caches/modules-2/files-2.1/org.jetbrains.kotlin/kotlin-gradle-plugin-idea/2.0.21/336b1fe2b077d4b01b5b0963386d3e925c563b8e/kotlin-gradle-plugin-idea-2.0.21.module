{"formatVersion": "1.1", "component": {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": "2.0.21", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.8"}}, "variants": [{"name": "publishedCompile", "attributes": {"org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-tooling-core", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-annotations", "version": {"requires": "2.0.21"}}]}, {"name": "publishedRuntime", "attributes": {"org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-tooling-core", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-annotations", "version": {"requires": "2.0.21"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-tooling-core", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-annotations", "version": {"requires": "2.0.21"}}], "files": [{"name": "kotlin-gradle-plugin-idea-2.0.21.jar", "url": "kotlin-gradle-plugin-idea-2.0.21.jar", "size": 94911, "sha512": "2e5bb1a3b718d3115ecbdce98230a46422291490bcf740498bac5a339552174cb8d1f2f384d378aa52c7c9bfc5a68322c9a246ea7a98c2d7fae31f787dc322c7", "sha256": "c1f4ea0c19267f1eed474b541b07715c49167acfbf027a8a2fd07cbbc81b8e72", "sha1": "dc54973fe68b0bb9b816bd04ef39648ca29526a6", "md5": "45377757691675bd89921bfd245d3a3f"}]}, {"name": "testFixturesApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}}], "files": [{"name": "kotlin-gradle-plugin-idea-2.0.21-test-fixtures.jar", "url": "kotlin-gradle-plugin-idea-2.0.21-test-fixtures.jar", "size": 51628, "sha512": "05bec89fc889369d4432ca7254ada89524acfd913188e93e2da081332fd33255b39a0b427666950277673c0e530c5906566318be5a5af3280d371c87208007ac", "sha256": "9f1014d4efafe2beb6c29a7a8d4ef8b4c8ded9e395721bb058deeb019da28e1b", "sha1": "709ef845038422dcf8fe6f416c95ae85abf64d05", "md5": "a472c486a25f5440a0f23a084b14eb06"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-idea-test-fixtures", "version": "2.0.21"}]}, {"name": "testFixturesRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-tooling-core", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea-proto", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-idea", "version": {"requires": "2.0.21"}}], "files": [{"name": "kotlin-gradle-plugin-idea-2.0.21-test-fixtures.jar", "url": "kotlin-gradle-plugin-idea-2.0.21-test-fixtures.jar", "size": 51628, "sha512": "05bec89fc889369d4432ca7254ada89524acfd913188e93e2da081332fd33255b39a0b427666950277673c0e530c5906566318be5a5af3280d371c87208007ac", "sha256": "9f1014d4efafe2beb6c29a7a8d4ef8b4c8ded9e395721bb058deeb019da28e1b", "sha1": "709ef845038422dcf8fe6f416c95ae85abf64d05", "md5": "a472c486a25f5440a0f23a084b14eb06"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-idea-test-fixtures", "version": "2.0.21"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-idea-2.0.21-javadoc.jar", "url": "kotlin-gradle-plugin-idea-2.0.21-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-idea-2.0.21-sources.jar", "url": "kotlin-gradle-plugin-idea-2.0.21-sources.jar", "size": 19666, "sha512": "df4792d6c50e3e0692e1807dcf2e71003b0e1f5cde85f211168798323d69d1016d5a5a3fd73ffeb51253f1a1da33645e06aaf3a2918393f8f80df33ab3e16d50", "sha256": "7d2c62633e1d654bb9400253d8e510e3744a109e403939423c74768e2b2fe062", "sha1": "703b885378c1e2c6a4ceddbac037850cde77ecf8", "md5": "70f2b22d3044f3d407a353840f1f0287"}]}]}